"""
黑客破译游戏核心组件测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core import Matrix, Sequence, GameConfig, Coordinate, HackingGame
from generator import PuzzleGenerator, AdvancedPuzzleGenerator
from solver import HackingSolver


def test_matrix():
    """测试矩阵类"""
    print("=== 测试矩阵类 ===")
    
    grid = [
        ["1C", "BD", "55"],
        ["E9", "7A", "FF"],
        ["1C", "BD", "55"]
    ]
    
    matrix = Matrix(grid, 3, 3)
    print(f"矩阵:\n{matrix}")
    
    coord = Coordinate(1, 0)
    print(f"坐标 {coord} 的值: {matrix.get_value(coord)}")
    
    print(f"第0行: {matrix.get_row(0)}")
    print(f"第1列: {matrix.get_column(1)}")
    print()


def test_sequence():
    """测试序列类"""
    print("=== 测试序列类 ===")
    
    sequence = Sequence(["1C", "BD"], "测试序列", 100)
    print(f"序列: {sequence}")
    print(f"长度: {len(sequence)}")
    print()


def test_game_config():
    """测试游戏配置"""
    print("=== 测试游戏配置 ===")
    
    config = GameConfig()
    print(f"矩阵大小: {config.matrix_width}x{config.matrix_height}")
    print(f"缓冲区大小: {config.buffer_size}")
    print(f"序列数量: {config.sequence_count}")
    print(f"可用值: {config.hex_values[:8]}...")
    print()


def test_puzzle_generator():
    """测试谜题生成器"""
    print("=== 测试谜题生成器 ===")
    
    config = GameConfig(matrix_width=4, matrix_height=4, buffer_size=6)
    generator = PuzzleGenerator(config)
    
    try:
        matrix, sequences = generator.generate_solvable_puzzle()
        print(f"生成的矩阵:\n{matrix}")
        print("\n生成的序列:")
        for i, seq in enumerate(sequences):
            print(f"{i+1}. {seq}")
        print()
        return matrix, sequences
    except Exception as e:
        print(f"生成谜题失败: {e}")
        return None, None


def test_solver(matrix, sequences):
    """测试求解器"""
    print("=== 测试求解器 ===")
    
    if not matrix or not sequences:
        print("没有有效的谜题可以测试")
        return
    
    solver = HackingSolver(matrix, sequences, 6)
    solution = solver.find_best_solution()
    
    if solution:
        print(f"找到解决方案!")
        print(f"路径: {[str(coord) for coord in solution.path]}")
        print(f"路径值: {solution.get_path_string(matrix)}")
        print(f"匹配序列: {solution.matched_sequences}")
        print(f"总奖励: {solution.total_reward}")
        print(f"路径长度: {solution.path_length}")
    else:
        print("未找到解决方案")
    print()


def test_game_validation():
    """测试游戏验证功能"""
    print("=== 测试游戏验证 ===")
    
    # 创建一个简单的测试矩阵
    grid = [
        ["1C", "BD", "55", "E9"],
        ["7A", "FF", "1C", "BD"],
        ["55", "E9", "7A", "FF"],
        ["1C", "BD", "55", "E9"]
    ]
    
    matrix = Matrix(grid, 4, 4)
    sequences = [
        Sequence(["1C", "BD"], "序列1", 100),
        Sequence(["BD", "55"], "序列2", 150),
    ]
    
    config = GameConfig(matrix_width=4, matrix_height=4, buffer_size=6)
    game = HackingGame(config)
    game.matrix = matrix
    game.sequences = sequences
    
    # 测试有效路径
    valid_path = [Coordinate(0, 0), Coordinate(1, 0), Coordinate(1, 1)]  # 1C -> BD -> FF
    is_valid, matched, reward = game.validate_player_solution(valid_path)
    
    print(f"测试路径: {[str(coord) for coord in valid_path]}")
    print(f"路径值: {[matrix.get_value(coord) for coord in valid_path]}")
    print(f"是否有效: {is_valid}")
    print(f"匹配序列: {matched}")
    print(f"总奖励: {reward}")
    
    # 测试无效路径（违反移动规则）
    invalid_path = [Coordinate(0, 0), Coordinate(0, 1)]  # 垂直移动作为第一步
    is_valid, matched, reward = game.validate_player_solution(invalid_path)
    
    print(f"\n测试无效路径: {[str(coord) for coord in invalid_path]}")
    print(f"是否有效: {is_valid}")
    print()


def test_advanced_generator():
    """测试高级生成器"""
    print("=== 测试高级生成器 ===")
    
    config = GameConfig(matrix_width=5, matrix_height=5, buffer_size=7)
    generator = AdvancedPuzzleGenerator(config)
    
    for difficulty in [1, 3, 5]:
        print(f"难度 {difficulty}:")
        generator.set_difficulty(difficulty)
        
        try:
            matrix, sequences = generator.generate_solvable_puzzle()
            print(f"  矩阵生成成功: {matrix.width}x{matrix.height}")
            print(f"  序列数量: {len(sequences)}")
            
            # 验证是否有解
            solver = HackingSolver(matrix, sequences, config.buffer_size)
            solution = solver.find_best_solution()
            print(f"  有解: {'是' if solution else '否'}")
            
        except Exception as e:
            print(f"  生成失败: {e}")
        print()


def run_all_tests():
    """运行所有测试"""
    print("🔓 开始黑客破译游戏核心组件测试\n")
    
    test_matrix()
    test_sequence()
    test_game_config()
    
    matrix, sequences = test_puzzle_generator()
    test_solver(matrix, sequences)
    
    test_game_validation()
    test_advanced_generator()
    
    print("✅ 所有测试完成!")


if __name__ == "__main__":
    run_all_tests()
