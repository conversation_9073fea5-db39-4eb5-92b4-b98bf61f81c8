"""
黑客破译游戏的核心数据结构和基础类
"""

from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum
import random


class Direction(Enum):
    """移动方向枚举"""
    HORIZONTAL = "horizontal"  # 水平移动（行）
    VERTICAL = "vertical"      # 垂直移动（列）


@dataclass
class Coordinate:
    """坐标类"""
    x: int  # 列
    y: int  # 行
    
    def __str__(self):
        return f"({self.x}, {self.y})"
    
    def __eq__(self, other):
        if not isinstance(other, Coordinate):
            return False
        return self.x == other.x and self.y == other.y
    
    def __hash__(self):
        return hash((self.x, self.y))


@dataclass
class SearchState:
    """搜索状态类，用于路径搜索算法"""
    pattern_index: int              # 当前匹配的序列位置
    used_positions: List[List[bool]]  # 已使用的位置矩阵
    path: List[Coordinate]          # 当前路径
    allowed_direction: Direction    # 下一步允许的移动方向
    current_pos: Coordinate         # 当前位置


@dataclass
class Sequence:
    """目标序列类"""
    values: List[str]  # 序列值
    name: str          # 序列名称
    reward: int        # 完成奖励
    
    def __len__(self):
        return len(self.values)
    
    def __str__(self):
        return f"{self.name}: {' '.join(self.values)}"


@dataclass
class Matrix:
    """游戏矩阵类"""
    grid: List[List[str]]  # 矩阵网格
    width: int             # 宽度
    height: int            # 高度
    
    def __post_init__(self):
        if not self.grid:
            raise ValueError("矩阵不能为空")
        self.height = len(self.grid)
        self.width = len(self.grid[0]) if self.grid else 0
        
        # 验证矩阵是否为矩形
        for row in self.grid:
            if len(row) != self.width:
                raise ValueError("矩阵必须是矩形的")
    
    def get_value(self, coord: Coordinate) -> str:
        """获取指定坐标的值"""
        if not self.is_valid_coordinate(coord):
            raise IndexError(f"坐标 {coord} 超出矩阵范围")
        return self.grid[coord.y][coord.x]
    
    def is_valid_coordinate(self, coord: Coordinate) -> bool:
        """检查坐标是否有效"""
        return 0 <= coord.x < self.width and 0 <= coord.y < self.height
    
    def get_row(self, y: int) -> List[str]:
        """获取指定行"""
        if not 0 <= y < self.height:
            raise IndexError(f"行索引 {y} 超出范围")
        return self.grid[y].copy()
    
    def get_column(self, x: int) -> List[str]:
        """获取指定列"""
        if not 0 <= x < self.width:
            raise IndexError(f"列索引 {x} 超出范围")
        return [self.grid[y][x] for y in range(self.height)]
    
    def __str__(self):
        """矩阵的字符串表示"""
        result = []
        for row in self.grid:
            result.append(" ".join(f"{val:>2}" for val in row))
        return "\n".join(result)


@dataclass
class Solution:
    """解决方案类"""
    path: List[Coordinate]          # 解决路径
    matched_sequences: List[int]    # 匹配的序列索引
    total_reward: int               # 总奖励
    path_length: int                # 路径长度
    
    def __post_init__(self):
        self.path_length = len(self.path)
    
    def get_path_string(self, matrix: Matrix) -> str:
        """获取路径对应的值序列字符串"""
        return " -> ".join(matrix.get_value(coord) for coord in self.path)


@dataclass
class GameConfig:
    """游戏配置类"""
    matrix_width: int = 6           # 矩阵宽度
    matrix_height: int = 6          # 矩阵高度
    buffer_size: int = 7            # 缓冲区大小（最大路径长度）
    sequence_count: int = 3         # 目标序列数量
    min_sequence_length: int = 2    # 最小序列长度
    max_sequence_length: int = 4    # 最大序列长度
    hex_values: List[str] = None    # 可用的十六进制值
    
    def __post_init__(self):
        if self.hex_values is None:
            # 默认使用常见的十六进制值
            self.hex_values = [
                "1C", "BD", "55", "E9", "7A", "FF", "1C", "BD", 
                "55", "E9", "7A", "FF", "1C", "BD", "55", "E9"
            ]


class HackingGame:
    """黑客破译游戏主类"""
    
    def __init__(self, config: GameConfig = None):
        self.config = config or GameConfig()
        self.matrix: Optional[Matrix] = None
        self.sequences: List[Sequence] = []
        self.solution: Optional[Solution] = None
        self.is_solved = False
    
    def generate_new_puzzle(self) -> bool:
        """生成新的谜题"""
        try:
            from .generator import PuzzleGenerator
        except ImportError:
            from generator import PuzzleGenerator
        generator = PuzzleGenerator(self.config)
        
        try:
            self.matrix, self.sequences = generator.generate_solvable_puzzle()
            self.solution = None
            self.is_solved = False
            return True
        except Exception as e:
            print(f"生成谜题失败: {e}")
            return False
    
    def solve_puzzle(self) -> Optional[Solution]:
        """求解当前谜题"""
        if not self.matrix or not self.sequences:
            return None
        
        try:
            from .solver import HackingSolver
        except ImportError:
            from solver import HackingSolver
        solver = HackingSolver(self.matrix, self.sequences, self.config.buffer_size)
        self.solution = solver.find_best_solution()
        return self.solution
    
    def validate_player_solution(self, path: List[Coordinate]) -> Tuple[bool, List[int], int]:
        """验证玩家提供的解决方案
        
        Returns:
            (是否有效, 匹配的序列索引列表, 总奖励)
        """
        if not self.matrix or not self.sequences:
            return False, [], 0
        
        if len(path) > self.config.buffer_size:
            return False, [], 0
        
        # 验证路径的有效性（交替行列移动）
        if not self._validate_path_rules(path):
            return False, [], 0
        
        # 检查匹配的序列
        matched_sequences = []
        total_reward = 0
        
        path_values = [self.matrix.get_value(coord) for coord in path]
        
        for i, sequence in enumerate(self.sequences):
            if self._sequence_matches_path(sequence.values, path_values):
                matched_sequences.append(i)
                total_reward += sequence.reward
        
        return len(matched_sequences) > 0, matched_sequences, total_reward
    
    def _validate_path_rules(self, path: List[Coordinate]) -> bool:
        """验证路径是否遵循游戏规则（交替行列移动）"""
        if len(path) < 2:
            return True
        
        for i in range(len(path) - 1):
            current = path[i]
            next_pos = path[i + 1]
            
            # 检查坐标是否有效
            if not self.matrix.is_valid_coordinate(current) or not self.matrix.is_valid_coordinate(next_pos):
                return False
            
            # 第一步必须是水平移动（在同一行）
            if i == 0:
                if current.y != next_pos.y:
                    return False
            else:
                # 之后必须交替移动
                prev_pos = path[i - 1]
                
                # 如果上一步是水平移动，这一步必须是垂直移动
                if current.y == prev_pos.y:  # 上一步是水平的
                    if current.x != next_pos.x:  # 这一步必须是垂直的
                        return False
                else:  # 上一步是垂直移动，这一步必须是水平移动
                    if current.y != next_pos.y:  # 这一步必须是水平的
                        return False
        
        return True
    
    def _sequence_matches_path(self, sequence: List[str], path_values: List[str]) -> bool:
        """检查序列是否在路径中匹配"""
        if len(sequence) > len(path_values):
            return False
        
        # 查找序列在路径中的连续匹配
        for i in range(len(path_values) - len(sequence) + 1):
            if path_values[i:i + len(sequence)] == sequence:
                return True
        
        return False
