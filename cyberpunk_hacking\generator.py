"""
黑客破译游戏的谜题生成器
确保生成的每个谜题都有解
"""

import random
from typing import List, Tuple, Set
try:
    from .core import Matrix, Sequence, GameConfig, Coordinate, Direction
except ImportError:
    from core import Matrix, Sequence, GameConfig, Coordinate, Direction


class PuzzleGenerator:
    """谜题生成器类"""
    
    def __init__(self, config: GameConfig):
        self.config = config
        self.max_attempts = 100  # 最大生成尝试次数
    
    def generate_solvable_puzzle(self) -> Tuple[Matrix, List[Sequence]]:
        """生成一个确保有解的谜题"""
        for attempt in range(self.max_attempts):
            try:
                matrix, sequences = self._generate_puzzle_attempt()
                
                # 验证谜题是否有解
                if self._verify_puzzle_solvable(matrix, sequences):
                    return matrix, sequences
                    
            except Exception as e:
                print(f"生成谜题尝试 {attempt + 1} 失败: {e}")
                continue
        
        # 如果所有尝试都失败，生成一个简单的保证有解的谜题
        return self._generate_simple_solvable_puzzle()
    
    def _generate_puzzle_attempt(self) -> Tuple[Matrix, List[Sequence]]:
        """尝试生成一个谜题"""
        # 生成矩阵
        matrix = self._generate_matrix()
        
        # 生成目标序列
        sequences = self._generate_sequences(matrix)
        
        return matrix, sequences
    
    def _generate_matrix(self) -> Matrix:
        """生成随机矩阵"""
        grid = []
        for y in range(self.config.matrix_height):
            row = []
            for x in range(self.config.matrix_width):
                # 随机选择一个十六进制值
                value = random.choice(self.config.hex_values)
                row.append(value)
            grid.append(row)
        
        return Matrix(grid, self.config.matrix_width, self.config.matrix_height)
    
    def _generate_sequences(self, matrix: Matrix) -> List[Sequence]:
        """基于矩阵生成目标序列"""
        sequences = []
        
        # 生成多个不同的序列
        for i in range(self.config.sequence_count):
            sequence_length = random.randint(
                self.config.min_sequence_length, 
                self.config.max_sequence_length
            )
            
            # 尝试从矩阵中提取一个可能的路径作为序列
            sequence_values = self._extract_sequence_from_matrix(matrix, sequence_length)
            
            if sequence_values:
                reward = self._calculate_sequence_reward(sequence_length, i)
                sequence = Sequence(
                    values=sequence_values,
                    name=f"序列{i + 1}",
                    reward=reward
                )
                sequences.append(sequence)
        
        # 如果没有生成足够的序列，补充随机序列
        while len(sequences) < self.config.sequence_count:
            sequence_length = random.randint(
                self.config.min_sequence_length, 
                self.config.max_sequence_length
            )
            sequence_values = [random.choice(self.config.hex_values) for _ in range(sequence_length)]
            reward = self._calculate_sequence_reward(sequence_length, len(sequences))
            
            sequence = Sequence(
                values=sequence_values,
                name=f"序列{len(sequences) + 1}",
                reward=reward
            )
            sequences.append(sequence)
        
        return sequences
    
    def _extract_sequence_from_matrix(self, matrix: Matrix, length: int) -> List[str]:
        """从矩阵中提取一个可能的序列路径"""
        # 随机选择起始位置
        start_x = random.randint(0, matrix.width - 1)
        start_y = random.randint(0, matrix.height - 1)
        
        path = [Coordinate(start_x, start_y)]
        current_direction = Direction.HORIZONTAL
        
        for step in range(length - 1):
            current_pos = path[-1]
            next_positions = self._get_valid_next_positions(
                matrix, current_pos, current_direction, set(path)
            )
            
            if not next_positions:
                break
            
            next_pos = random.choice(next_positions)
            path.append(next_pos)
            
            # 切换方向
            current_direction = (Direction.VERTICAL if current_direction == Direction.HORIZONTAL 
                               else Direction.HORIZONTAL)
        
        # 提取路径对应的值
        return [matrix.get_value(coord) for coord in path]
    
    def _get_valid_next_positions(self, matrix: Matrix, current: Coordinate, 
                                direction: Direction, used: Set[Coordinate]) -> List[Coordinate]:
        """获取下一步的有效位置"""
        positions = []
        
        if direction == Direction.HORIZONTAL:
            # 水平移动：同一行的其他列
            for x in range(matrix.width):
                if x != current.x:
                    coord = Coordinate(x, current.y)
                    if coord not in used:
                        positions.append(coord)
        else:
            # 垂直移动：同一列的其他行
            for y in range(matrix.height):
                if y != current.y:
                    coord = Coordinate(current.x, y)
                    if coord not in used:
                        positions.append(coord)
        
        return positions
    
    def _calculate_sequence_reward(self, length: int, priority: int) -> int:
        """计算序列奖励"""
        base_reward = 100
        length_bonus = (length - 1) * 50
        priority_bonus = (self.config.sequence_count - priority) * 25
        return base_reward + length_bonus + priority_bonus
    
    def _verify_puzzle_solvable(self, matrix: Matrix, sequences: List[Sequence]) -> bool:
        """验证谜题是否有解"""
        try:
            from .solver import HackingSolver
        except ImportError:
            from solver import HackingSolver
        
        try:
            solver = HackingSolver(matrix, sequences, self.config.buffer_size)
            solution = solver.find_best_solution()
            return solution is not None and len(solution.matched_sequences) > 0
        except Exception:
            return False
    
    def _generate_simple_solvable_puzzle(self) -> Tuple[Matrix, List[Sequence]]:
        """生成一个简单的保证有解的谜题"""
        # 创建一个简单的矩阵
        grid = []
        values = ["1C", "BD", "55", "E9"]
        
        for y in range(self.config.matrix_height):
            row = []
            for x in range(self.config.matrix_width):
                # 使用循环模式确保有规律
                value = values[(x + y) % len(values)]
                row.append(value)
            grid.append(row)
        
        matrix = Matrix(grid, self.config.matrix_width, self.config.matrix_height)
        
        # 创建简单的序列
        sequences = [
            Sequence(["1C", "BD"], "简单序列1", 150),
            Sequence(["BD", "55"], "简单序列2", 175),
            Sequence(["55", "E9"], "简单序列3", 200)
        ]
        
        return matrix, sequences


class AdvancedPuzzleGenerator(PuzzleGenerator):
    """高级谜题生成器，支持更复杂的生成策略"""
    
    def __init__(self, config: GameConfig):
        super().__init__(config)
        self.difficulty_level = 1  # 难度级别
    
    def set_difficulty(self, level: int):
        """设置难度级别 (1-5)"""
        self.difficulty_level = max(1, min(5, level))
    
    def _generate_matrix(self) -> Matrix:
        """根据难度生成矩阵"""
        # 根据难度调整值的多样性
        if self.difficulty_level <= 2:
            # 简单：使用较少的不同值
            available_values = self.config.hex_values[:6]
        elif self.difficulty_level <= 4:
            # 中等：使用更多值
            available_values = self.config.hex_values[:10]
        else:
            # 困难：使用所有值
            available_values = self.config.hex_values
        
        grid = []
        for y in range(self.config.matrix_height):
            row = []
            for x in range(self.config.matrix_width):
                value = random.choice(available_values)
                row.append(value)
            grid.append(row)
        
        return Matrix(grid, self.config.matrix_width, self.config.matrix_height)
    
    def _generate_sequences(self, matrix: Matrix) -> List[Sequence]:
        """根据难度生成序列"""
        sequences = []
        
        # 根据难度调整序列长度
        if self.difficulty_level <= 2:
            min_len, max_len = 2, 3
        elif self.difficulty_level <= 4:
            min_len, max_len = 2, 4
        else:
            min_len, max_len = 3, 5
        
        for i in range(self.config.sequence_count):
            sequence_length = random.randint(min_len, max_len)
            
            # 高难度下，有更高概率生成完全随机的序列
            if self.difficulty_level >= 4 and random.random() < 0.3:
                sequence_values = [random.choice(self.config.hex_values) for _ in range(sequence_length)]
            else:
                sequence_values = self._extract_sequence_from_matrix(matrix, sequence_length)
                if not sequence_values:
                    sequence_values = [random.choice(self.config.hex_values) for _ in range(sequence_length)]
            
            reward = self._calculate_sequence_reward(sequence_length, i)
            sequence = Sequence(
                values=sequence_values,
                name=f"目标{i + 1}",
                reward=reward
            )
            sequences.append(sequence)
        
        return sequences
