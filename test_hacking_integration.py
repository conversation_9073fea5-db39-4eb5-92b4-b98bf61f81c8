"""
测试黑客破译模块与机器人的集成
"""

import sys
import os

def test_module_import():
    """测试模块导入"""
    print("🔍 测试黑客破译模块导入...")
    
    try:
        from plugins.xiuxian_game.entertainment.cyberpunk_hacking import core, generator, solver, commands
        print("✅ 核心模块导入成功")
        
        # 测试基本功能
        config = core.GameConfig(matrix_width=4, matrix_height=4, buffer_size=5)
        game = core.HackingGame(config)
        
        print("✅ 游戏实例创建成功")
        
        # 测试谜题生成
        if game.generate_new_puzzle():
            print("✅ 谜题生成成功")
            print(f"   矩阵大小: {game.matrix.width}x{game.matrix.height}")
            print(f"   序列数量: {len(game.sequences)}")
            
            # 测试求解
            solution = game.solve_puzzle()
            if solution:
                print("✅ 求解器工作正常")
                print(f"   找到解决方案，匹配 {len(solution.matched_sequences)} 个序列")
            else:
                print("⚠️  未找到解决方案")
        else:
            print("❌ 谜题生成失败")
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    
    return True


def test_command_registration():
    """测试命令注册"""
    print("\n🔍 测试命令注册...")
    
    try:
        # 检查命令是否正确注册
        from plugins.xiuxian_game.entertainment.cyberpunk_hacking.commands import hack_start, hack_solve, hack_hint, hack_rules
        print("✅ 命令处理器导入成功")
        
        # 检查命令配置
        print(f"   开始游戏命令: {hack_start.rule}")
        print(f"   提交解答命令: {hack_solve.rule}")
        print(f"   查看提示命令: {hack_hint.rule}")
        print(f"   查看规则命令: {hack_rules.rule}")
        
    except ImportError as e:
        print(f"❌ 命令模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 命令测试过程中出错: {e}")
        return False
    
    return True


def test_game_flow():
    """测试完整游戏流程"""
    print("\n🔍 测试完整游戏流程...")
    
    try:
        from plugins.xiuxian_game.entertainment.cyberpunk_hacking.core import HackingGame, GameConfig, Coordinate
        
        # 创建游戏
        config = GameConfig(matrix_width=4, matrix_height=4, buffer_size=6)
        game = HackingGame(config)
        
        # 生成谜题
        if not game.generate_new_puzzle():
            print("❌ 谜题生成失败")
            return False
        
        print("✅ 谜题生成成功")
        
        # 显示矩阵
        print("\n📋 生成的矩阵:")
        for y, row in enumerate(game.matrix.grid):
            print(f"   {y}: {' '.join(f'{val:>2}' for val in row)}")
        
        print("\n🎯 目标序列:")
        for i, seq in enumerate(game.sequences):
            print(f"   {i+1}. {seq.name}: {' → '.join(seq.values)} (奖励: {seq.reward})")
        
        # 获取解决方案
        solution = game.solve_puzzle()
        if solution:
            print(f"\n💡 找到解决方案:")
            print(f"   路径: {' → '.join(f'({c.x},{c.y})' for c in solution.path)}")
            print(f"   值序列: {solution.get_path_string(game.matrix)}")
            print(f"   匹配序列: {solution.matched_sequences}")
            print(f"   总奖励: {solution.total_reward}")
            
            # 验证解决方案
            is_valid, matched, reward = game.validate_player_solution(solution.path)
            print(f"   验证结果: {'有效' if is_valid else '无效'}")
            
        else:
            print("⚠️  未找到解决方案")
        
    except Exception as e:
        print(f"❌ 游戏流程测试失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🔓 开始黑客破译模块集成测试\n")
    
    success_count = 0
    total_tests = 3
    
    # 运行测试
    if test_module_import():
        success_count += 1
    
    if test_command_registration():
        success_count += 1
    
    if test_game_flow():
        success_count += 1
    
    # 输出结果
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！黑客破译模块集成成功！")
        print("\n📝 使用说明:")
        print("   1. 发送 '黑客破译' 或 '黑客破译 3' 开始游戏")
        print("   2. 发送 '破译 (0,0) (1,0) (1,1)' 提交解答")
        print("   3. 发送 '破译提示' 查看解决方案")
        print("   4. 发送 '破译规则' 查看游戏规则")
    else:
        print("❌ 部分测试失败，请检查模块配置")
    
    return success_count == total_tests


if __name__ == "__main__":
    main()
